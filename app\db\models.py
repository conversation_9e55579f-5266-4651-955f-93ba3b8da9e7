from __future__ import annotations

import hashlib
import json
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple
from uuid import uuid4

import numpy as np
from pydantic import BaseModel
from sqlalchemy import (
    JSON,
    TIMESTAMP,
    UUID,
    Boolean,
    CheckConstraint,
    Column,
    Float,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    UniqueConstraint,
    create_engine,
    select,
    text,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import DeclarativeBase, Mapped, Session, mapped_column, relationship
from sqlalchemy.pool import NullPool


class Base(DeclarativeBase):
    pass


class Document(Base):
    __tablename__ = "documents"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True)
    source_type: Mapped[str] = mapped_column(String(32), nullable=False)
    source_url: Mapped[str] = mapped_column(Text, nullable=False)
    vendor_id: Mapped[Optional[str]] = mapped_column(String(128))
    published_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True), index=True, nullable=False
    )
    language: Mapped[Optional[str]] = mapped_column(String(16))
    title: Mapped[Optional[str]] = mapped_column(Text)
    raw_text: Mapped[str] = mapped_column(Text, nullable=False)
    normalized_text: Mapped[Optional[str]] = mapped_column(Text)
    content_sha256: Mapped[str] = mapped_column(String(64), index=True)
    simhash64: Mapped[Optional[str]] = mapped_column(String(16), index=True)
    embedding: Mapped[Optional[List[float]]] = mapped_column(JSONB)
    credibility_score: Mapped[Optional[float]] = mapped_column(Float)
    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc)
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )


Index("ix_documents_published_at_source", Document.published_at, Document.source_type)


class DocumentTicker(Base):
    __tablename__ = "document_tickers"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    document_id: Mapped[str] = mapped_column(
        ForeignKey("documents.id", ondelete="CASCADE"), index=True
    )
    ticker: Mapped[str] = mapped_column(String(16), index=True)
    figi: Mapped[Optional[str]] = mapped_column(String(16))
    confidence: Mapped[float] = mapped_column(Float)


class DocExtraction(Base):
    __tablename__ = "extractions"

    document_id: Mapped[str] = mapped_column(
        ForeignKey("documents.id", ondelete="CASCADE"),
        primary_key=True,
    )
    event_type: Mapped[str] = mapped_column(String(64), index=True)
    effective_datetime: Mapped[Optional[datetime]] = mapped_column(
        TIMESTAMP(timezone=True), index=True
    )
    sentiment_polarity: Mapped[str] = mapped_column(String(8))
    sentiment_intensity: Mapped[float] = mapped_column(Float)
    severity: Mapped[Optional[float]] = mapped_column(Float)
    numeric_fields: Mapped[Dict[str, float]] = mapped_column(JSONB)
    eight_k_items: Mapped[List[str]] = mapped_column(JSONB)
    uncertainty_flags: Mapped[List[str]] = mapped_column(JSONB)
    novelty_score: Mapped[Optional[float]] = mapped_column(Float)
    relevance_score: Mapped[Optional[float]] = mapped_column(Float)
    credibility_score: Mapped[Optional[float]] = mapped_column(Float)


class DocCitation(Base):
    __tablename__ = "citations"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True)
    document_id: Mapped[str] = mapped_column(
        ForeignKey("documents.id", ondelete="CASCADE"), index=True
    )
    field: Mapped[str] = mapped_column(String(64))
    start_char: Mapped[int] = mapped_column(Integer)
    end_char: Mapped[int] = mapped_column(Integer)
    quote: Mapped[str] = mapped_column(Text)


class EventCluster(Base):
    __tablename__ = "event_clusters"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True)
    event_type: Mapped[str] = mapped_column(String(64), index=True)
    primary_ticker: Mapped[Optional[str]] = mapped_column(String(16), index=True)
    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc)
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )
    canonical_effective_datetime: Mapped[Optional[datetime]] = mapped_column(
        TIMESTAMP(timezone=True), index=True
    )
    canonical_payload: Mapped[Dict[str, Any]] = mapped_column(JSONB)
    status: Mapped[str] = mapped_column(String(16), default="open")


class EventDocument(Base):
    __tablename__ = "event_documents"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True)
    event_id: Mapped[str] = mapped_column(
        ForeignKey("event_clusters.id", ondelete="CASCADE"), index=True
    )
    document_id: Mapped[str] = mapped_column(
        ForeignKey("documents.id", ondelete="CASCADE"), index=True
    )
    role: Mapped[str] = mapped_column(String(16), default="supporting")
    doc_weight: Mapped[float] = mapped_column(Float)
    first_seen: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc)
    )


class EventTarget(Base):
    __tablename__ = "event_targets"

    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True)
    event_id: Mapped[str] = mapped_column(
        ForeignKey("event_clusters.id", ondelete="CASCADE"), index=True
    )
    ticker: Mapped[str] = mapped_column(String(16), index=True)
    figi: Mapped[Optional[str]] = mapped_column(String(16))
    role: Mapped[str] = mapped_column(String(16), default="primary")
    relevance_score: Mapped[float] = mapped_column(Float)
    weight: Mapped[float] = mapped_column(Float)


class EventFeatures(Base):
    __tablename__ = "event_features"

    event_id: Mapped[str] = mapped_column(
        ForeignKey("event_clusters.id", ondelete="CASCADE"),
        primary_key=True,
    )
    features: Mapped[Dict[str, Any]] = mapped_column(JSONB)
    computed_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc)
    )


def create_all(url: str) -> None:
    engine = create_engine(url, poolclass=NullPool, future=True)
    Base.metadata.create_all(engine)


# Lightweight helpers (placeholders for demo; replace with robust impls)

def normalize_text(text: str) -> str:
    return " ".join(text.split()).lower().strip()


def sha256_hex(s: str) -> str:
    return hashlib.sha256(s.encode("utf-8")).hexdigest()


def simple_embed(text: str) -> list[float]:
    # Placeholder deterministic 64-d vector
    seed = int(hashlib.md5(text.encode()).hexdigest()[:8], 16)
    rng = np.random.default_rng(seed)
    v = rng.standard_normal(64)
    v = v / (np.linalg.norm(v) + 1e-8)
    return v.astype(np.float32).tolist()
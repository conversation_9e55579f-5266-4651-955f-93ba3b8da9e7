from __future__ import annotations

import os
from dataclasses import dataclass


@dataclass(frozen=True)
class Topics:
    RAW_DOCS: str = "raw.documents"
    EXTRACTED_VALID: str = "extracted.valid"
    EXTRACTED_DLQ: str = "extracted.invalid.dlq"
    CLUSTERS_UPDATED: str = "clusters.updated"
    FEATURES_READY: str = "features.ready"
    PREDICTIONS_SCORED: str = "predictions.scored"
    AUDIT_EVENTS: str = "audit.events"


def brokers() -> str:
    return os.environ.get("KAFKA_BROKERS", "localhost:9092")


def group(service_name: str) -> str:
    env = os.environ.get("ENV", "dev")
    return f"{service_name}.{env}.g"
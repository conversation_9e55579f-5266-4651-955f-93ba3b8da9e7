from __future__ import annotations

import asyncio
import os
import traceback
from datetime import datetime, timezone

import ujson
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer

from app.extraction.extract import extract_with_retries
from app.streaming.kafka_config import Topics, brokers, group


SERVICE = "extractor"


async def run() -> None:
    consumer = AIOKafkaConsumer(
        Topics.RAW_DOCS,
        bootstrap_servers=brokers(),
        group_id=group(SERVICE),
        enable_auto_commit=False,
        value_deserializer=lambda v: ujson.loads(v) if v else None,
        key_deserializer=lambda v: v.decode() if v else None,
        auto_offset_reset="earliest",
    )
    producer = AIOKafkaProducer(
        bootstrap_servers=brokers(),
        linger_ms=5,
        acks="all",
        enable_idempotence=True,
        value_serializer=lambda v: ujson.dumps(v).encode(),
        key_serializer=lambda v: v.encode(),
    )

    await consumer.start()
    await producer.start()
    try:
        async for msg in consumer:
            key = msg.key or ""
            doc = msg.value or {}
            article = doc.get("text") or doc.get("raw_text") or ""
            try:
                extraction = extract_with_retries(
                    article_text=article,
                    provider=os.environ.get("LLM_PROVIDER", "openai"),
                    max_tries=3,
                )
                payload = extraction.model_dump()
                await producer.send_and_wait(
                    Topics.EXTRACTED_VALID, key=key, value=payload
                )
                await consumer.commit()
            except Exception as e:
                error_info = {
                    "error": str(e),
                    "trace": traceback.format_exc()[:2000],
                    "doc_key": key,
                    "seen_at": datetime.now(timezone.utc).isoformat(),
                }
                await producer.send_and_wait(
                    Topics.EXTRACTED_DLQ, key=key, value=error_info
                )
                await consumer.commit()
    finally:
        await consumer.stop()
        await producer.stop()


if __name__ == "__main__":
    asyncio.run(run())
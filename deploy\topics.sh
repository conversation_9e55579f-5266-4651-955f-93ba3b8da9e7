#!/usr/bin/env bash
set -euo pipefail

BROKERS="${KAFKA_BROKERS:-localhost:9092}"

create_topic() {
  local topic="$1"
  docker exec -i redpanda rpk topic create "$topic" \
    --brokers redpanda:9092 \
    --replicas 1 \
    --partitions 6 \
    --config retention.ms=$((7*24*3600*1000)) \
    --config cleanup.policy=delete || true
}

create_topic "raw.documents"
create_topic "extracted.valid"
create_topic "extracted.invalid.dlq"
create_topic "clusters.updated"
create_topic "features.ready"
create_topic "predictions.scored"
create_topic "audit.events"

echo "Topics created."
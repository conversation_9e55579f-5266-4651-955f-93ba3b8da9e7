from __future__ import annotations

import os
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Any, Dict

import lightgbm as lgb
import numpy as np
import pandas as pd
from feast import FeatureStore


def load_training_data() -> pd.DataFrame:
    """
    Example point-in-time correct retrieval using Feast.
    Assumes you have:
      - <PERSON>rquet at data/offline/event_features.parquet
      - Labels at data/offline/labels.parquet with columns:
        event_id, event_ts, label_return_abn_1d
    """
    repo = os.environ.get("FEAST_REPO", "app/feature_store/feature_repo")
    fs = FeatureStore(repo_path=repo)

    labels = pd.read_parquet("data/offline/labels.parquet")
    entity_df = labels.rename(columns={"event_ts": "event_timestamp"})[
        ["event_id", "event_timestamp"]
    ]

    training_df = fs.get_historical_features(
        entity_df=entity_df, features=["event_features_v1:*"]
    ).to_df()

    df = training_df.merge(labels, on=["event_id"], how="inner")
    return df


def train_and_save(df: pd.DataFrame) -> None:
    # Minimal feature set, align with predict_service.vectorize()
    cols = [
        "impact_weight",
        "severity",
        "sentiment_intensity",
        "disagreement",
        "support_doc_count",
        "support_weight_sum",
    ]
    X = df[cols].astype(np.float32).values
    y = df["label_return_abn_1d"].astype(np.float32).values

    params = {
        "objective": "regression",
        "metric": ["l2", "mape"],
        "learning_rate": 0.05,
        "num_leaves": 31,
        "feature_fraction": 0.9,
        "bagging_fraction": 0.8,
        "bagging_freq": 1,
        "seed": 42,
    }
    dtrain = lgb.Dataset(X, label=y, feature_name=cols)
    booster = lgb.train(params, dtrain, num_boost_round=500)

    Path("models").mkdir(exist_ok=True)
    booster.save_model("models/lgbm_event_model.txt")
    print("Saved model to models/lgbm_event_model.txt")


if __name__ == "__main__":
    df = load_training_data()
    print("Training rows:", len(df))
    train_and_save(df)
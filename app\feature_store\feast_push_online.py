from __future__ import annotations

import os
from datetime import datetime
from typing import Any, Dict, Iterable

from feast import FeatureStore


def push_events_to_online(
    rows: Iterable[tuple[str, Dict[str, Any], datetime]]
) -> None:
    """
    Push event features to Feast online store.

    rows: iterable of (event_id, feature_dict, computed_at)
    """
    repo_path = os.environ.get("FEAST_REPO", "app/feature_store/feature_repo")
    fs = FeatureStore(repo_path=repo_path)
    data = []
    for event_id, feats, ts in rows:
        item = {"event_id": event_id, "event_features_v1": feats, "event_ts": ts}
        data.append(item)
    fs.push("event_features_v1", data)
from __future__ import annotations

import asyncio
import json
import os
from pathlib import Path
from typing import Any, Dict

import numpy as np
import ujson
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from lightgbm import Booster

from app.streaming.kafka_config import Topics, brokers, group


SERVICE = "predictor"


def load_model(path: str) -> Booster:
    return Booster(model_file=path)


def vectorize(features: Dict[str, Any]) -> tuple[np.ndarray, list[str]]:
    # Minimal deterministic column order
    cols = [
        "impact_weight",
        "severity",
        "sentiment_intensity",
        "disagreement",
        "support_doc_count",
        "support_weight_sum",
    ]
    x = np.array([[float(features.get(c, 0.0)) for c in cols]], dtype=np.float32)
    return x, cols


async def run() -> None:
    model_path = os.environ.get("MODEL_PATH", "models/lgbm_event_model.txt")
    booster = load_model(model_path)

    consumer = AIOKafkaConsumer(
        Topics.FEATURES_READY,
        bootstrap_servers=brokers(),
        group_id=group(SERVICE),
        enable_auto_commit=False,
        value_deserializer=lambda v: ujson.loads(v) if v else None,
        key_deserializer=lambda v: v.decode() if v else None,
        auto_offset_reset="earliest",
    )
    producer = AIOKafkaProducer(
        bootstrap_servers=brokers(),
        linger_ms=5,
        acks="all",
        enable_idempotence=True,
        value_serializer=lambda v: ujson.dumps(v).encode(),
        key_serializer=lambda v: v.encode(),
    )

    await consumer.start()
    await producer.start()
    try:
        async for msg in consumer:
            event_id = msg.key or ""
            feats = (msg.value or {}).get("features", {})
            x, cols = vectorize(feats)
            yhat = float(booster.predict(x)[0])
            await producer.send_and_wait(
                Topics.PREDICTIONS_SCORED,
                key=event_id,
                value={
                    "event_id": event_id,
                    "prediction": yhat,
                    "feature_keys": cols,
                },
            )
            await consumer.commit()
    finally:
        await consumer.stop()
        await producer.stop()


if __name__ == "__main__":
    asyncio.run(run())
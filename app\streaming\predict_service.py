from __future__ import annotations

import asyncio
import os
from typing import Any, Dict

import numpy as np
import ujson
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from lightgbm import Booster

from app.streaming.kafka_config import Topics, brokers, group


SERVICE = "predictor"


def load_model(path: str) -> Booster:
    return Booster(model_file=path)


def vectorize(features: Dict[str, Any]) -> tuple[np.ndarray, list[str]]:
    """Convert feature dictionary to numpy array for model prediction."""
    # Minimal deterministic column order - must match training script
    column_names = [
        "impact_weight",
        "severity",
        "sentiment_intensity",
        "disagreement",
        "support_doc_count",
        "support_weight_sum",
    ]
    feature_vector = np.array(
        [[float(features.get(col, 0.0)) for col in column_names]],
        dtype=np.float32
    )
    return feature_vector, column_names


async def run() -> None:
    model_path = os.environ.get("MODEL_PATH", "models/lgbm_event_model.txt")
    booster = load_model(model_path)

    consumer = AIOKafkaConsumer(
        Topics.FEATURES_READY,
        bootstrap_servers=brokers(),
        group_id=group(SERVICE),
        enable_auto_commit=False,
        value_deserializer=lambda v: ujson.loads(v) if v else None,
        key_deserializer=lambda v: v.decode() if v else None,
        auto_offset_reset="earliest",
    )
    producer = AIOKafkaProducer(
        bootstrap_servers=brokers(),
        linger_ms=5,
        acks="all",
        enable_idempotence=True,
        value_serializer=lambda v: ujson.dumps(v).encode(),
        key_serializer=lambda v: v.encode(),
    )

    await consumer.start()
    await producer.start()
    try:
        async for msg in consumer:
            event_id = msg.key or ""
            features = (msg.value or {}).get("features", {})
            feature_vector, column_names = vectorize(features)
            prediction = float(booster.predict(feature_vector)[0])
            await producer.send_and_wait(
                Topics.PREDICTIONS_SCORED,
                key=event_id,
                value={
                    "event_id": event_id,
                    "prediction": prediction,
                    "feature_keys": column_names,
                },
            )
            await consumer.commit()
    finally:
        await consumer.stop()
        await producer.stop()


if __name__ == "__main__":
    asyncio.run(run())
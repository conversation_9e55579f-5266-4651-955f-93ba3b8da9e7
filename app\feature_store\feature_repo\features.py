from __future__ import annotations

from datetime import timedelta

from feast import Entity, FeatureView, Field, FileSource
from feast.types import Float32, Int64, String

# Offline source: a Parquet export of event_features for training
event_source = FileSource(
    path="data/offline/event_features.parquet",
    timestamp_field="computed_at",
)

event = Entity(name="event", join_keys=["event_id"])

event_features_v1 = FeatureView(
    name="event_features_v1",
    entities=[event],
    ttl=timedelta(days=30),
    schema=[
        Field(name="impact_weight", dtype=Float32),
        Field(name="severity", dtype=Float32),
        <PERSON>(name="sentiment_intensity", dtype=Float32),
        Field(name="disagreement", dtype=Float32),
        <PERSON>(name="support_doc_count", dtype=Int64),
        Field(name="support_weight_sum", dtype=Float32),
        <PERSON>(name="event_type", dtype=String),
        <PERSON>(name="primary_ticker", dtype=String),
        <PERSON>(name="target_count", dtype=Int64),
    ],
    online=True,
    source=event_source,
)
from __future__ import annotations

import json
import math
from collections import Counter
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Iterable, List, Optional, Set, Tuple
from uuid import uuid4

import numpy as np
from sqlalchemy import select
from sqlalchemy.orm import Session

from app.db.models import (
    DocCitation,
    DocExtraction,
    Document,
    DocumentTicker,
    EventCluster,
    EventDocument,
    EventFeatures,
    EventTarget,
    sha256_hex,
    simple_embed,
)
from app.extraction.schema import Extraction, Mention


def upsert_document_with_extraction(
    session: Session,
    extraction: Extraction,
    title: Optional[str],
    raw_text: str,
    language: Optional[str],
) -> tuple[Document, DocExtraction]:
    norm = " ".join(raw_text.split()).strip().lower()
    doc = Document(
        id=str(uuid4()),
        source_type=extraction.source_type,
        source_url=extraction.source_url,
        vendor_id=extraction.vendor_id,
        published_at=datetime.fromisoformat(extraction.published_at),
        language=language,
        title=title,
        raw_text=raw_text,
        normalized_text=norm,
        content_sha256=sha256_hex(norm),
        simhash64=sha256_hex(norm)[:16],
        embedding=simple_embed(norm),
        credibility_score=extraction.credibility_score or 0.8,
    )
    session.add(doc)
    session.flush()

    for m in extraction.mentions:
        session.add(
            DocumentTicker(
                document_id=doc.id,
                ticker=m.ticker,
                figi=m.figi,
                confidence=m.confidence,
            )
        )

    eff_dt = (
        datetime.fromisoformat(extraction.effective_datetime)
        if extraction.effective_datetime
        else None
    )
    de = DocExtraction(
        document_id=doc.id,
        event_type=extraction.event_type,
        effective_datetime=eff_dt,
        sentiment_polarity=extraction.sentiment_polarity,
        sentiment_intensity=extraction.sentiment_intensity,
        severity=extraction.severity,
        numeric_fields=extraction.numeric_fields,
        eight_k_items=extraction.eight_k_items,
        uncertainty_flags=extraction.uncertainty_flags,
        novelty_score=extraction.novelty_score,
        relevance_score=extraction.relevance_score,
        credibility_score=extraction.credibility_score,
    )
    session.add(de)
    session.flush()

    for c in extraction.citations:
        session.add(
            DocCitation(
                id=str(uuid4()),
                document_id=doc.id,
                field=c.field,
                start_char=c.start_char,
                end_char=c.end_char,
                quote=c.quote,
            )
        )
    session.flush()
    return doc, de


def extract_primary_ticker(mentions: List[Mention]) -> Optional[str]:
    if not mentions:
        return None
    return sorted(mentions, key=lambda m: (-m.confidence, m.ticker))[0].ticker


def ticker_set(session: Session, document_id: str) -> Set[str]:
    rows = session.execute(
        select(DocumentTicker.ticker).where(
            DocumentTicker.document_id == document_id
        )
    ).all()
    return {r[0] for r in rows}


def cosine(a: np.ndarray, b: np.ndarray) -> float:
    return float(np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b) + 1e-8))


def doc_impact_weight(
    severity: float,
    novelty: float,
    relevance: float,
    credibility: float,
    timing: float,
    uncertainty_penalty: float,
    regime: float,
) -> float:
    x = (
        1.6 * severity
        + 0.9 * novelty
        + 1.0 * relevance
        + 0.8 * credibility
        + 0.4 * timing
        - 0.8 * uncertainty_penalty
        + 0.2 * regime
    )
    return 1.0 / (1.0 + math.exp(-x))


def fetch_candidate_clusters(
    session: Session,
    evt_type: str,
    primary_ticker: Optional[str],
    published_at: datetime,
    window_sec: int,
) -> List[EventCluster]:
    t0 = published_at - timedelta(seconds=window_sec)
    t1 = published_at + timedelta(seconds=window_sec)
    stmt = select(EventCluster).where(
        EventCluster.created_at >= t0, EventCluster.created_at <= t1
    )
    if evt_type:
        stmt = stmt.where(EventCluster.event_type == evt_type)
    if primary_ticker:
        stmt = stmt.where(EventCluster.primary_ticker == primary_ticker)
    return list(session.execute(stmt).scalars())


def attach_or_create_cluster(
    session: Session,
    doc: Document,
    de: DocExtraction,
    extraction: Extraction,
    window_sec: int = 12 * 3600,
    text_cosine_min: float = 0.63,
) -> EventCluster:
    primary_ticker = extract_primary_ticker(extraction.mentions)
    candidates = fetch_candidate_clusters(
        session, de.event_type, primary_ticker, doc.published_at, window_sec
    )

    doc_vec = np.array(doc.embedding or [], dtype=np.float32)
    best_cluster = None
    best_score = -1.0

    for c in candidates:
        if c.documents:
            ref_doc = c.documents[0].document
            ref_vec = np.array(ref_doc.embedding or [], dtype=np.float32)
            sim = cosine(doc_vec, ref_vec) if len(doc_vec) == len(ref_vec) else 0.0
        else:
            sim = 0.0
        doc_tks = ticker_set(session, doc.id)
        cand_tks = set(t.ticker for t in c.targets)
        overlap = len(doc_tks & cand_tks)
        score = 0.6 * sim + 0.4 * min(1.0, overlap)
        if score > best_score:
            best_score = score
            best_cluster = c

    if best_cluster and best_score >= text_cosine_min:
        cluster = best_cluster
    else:
        cluster = EventCluster(
            id=str(uuid4()),
            event_type=de.event_type,
            primary_ticker=primary_ticker,
            canonical_effective_datetime=de.effective_datetime,
            canonical_payload={},
            status="open",
        )
        session.add(cluster)
        session.flush()
        for m in extraction.mentions:
            session.add(
                EventTarget(
                    id=str(uuid4()),
                    event_id=cluster.id,
                    ticker=m.ticker,
                    figi=m.figi,
                    role="primary" if m.ticker == primary_ticker else "secondary",
                    relevance_score=m.confidence,
                    weight=m.confidence,
                )
            )

    sev = float(de.severity or 0.0)
    nov = float(de.novelty_score or 0.5)
    rel = float(de.relevance_score or 0.6)
    cred = float(de.credibility_score or 0.8)
    timing = 1.0
    uncert = min(1.0, 0.2 * len(de.uncertainty_flags))
    regime = 0.5

    session.add(
        EventDocument(
            id=str(uuid4()),
            event_id=cluster.id,
            document_id=doc.id,
            role="supporting",
            doc_weight=doc_impact_weight(sev, nov, rel, cred, timing, uncert, regime),
        )
    )
    session.flush()
    aggregate_cluster(session, cluster.id)
    return cluster


def aggregate_numeric_field(rows: List[Tuple[float, float]]) -> Optional[float]:
    if not rows:
        return None
    rows = sorted(rows, key=lambda x: x[0])
    total = sum(w for _, w in rows)
    acc = 0.0
    for v, w in rows:
        acc += w
        if acc >= 0.5 * total:
            return float(v)
    return float(rows[-1][0])


def aggregate_cluster(session: Session, event_id: str) -> None:
    ev = session.get(EventCluster, event_id)
    docs = session.execute(
        select(EventDocument).where(EventDocument.event_id == event_id)
    ).scalars().all()

    evt_types: List[str] = []
    sentiments: List[tuple[str, float]] = []
    severities: List[float] = []
    eff_times: List[datetime] = []
    numeric_values: Dict[str, List[Tuple[float, float]]] = {}

    weight_sum = 0.0
    for ed in docs:
        d = session.get(Document, ed.document_id)
        de = session.get(DocExtraction, ed.document_id)
        if not d or not de:
            continue
        w = ed.doc_weight
        weight_sum += w
        evt_types.append(de.event_type)
        sentiments.append((de.sentiment_polarity, w))
        if de.severity is not None:
            severities.append(de.severity * w)
        if de.effective_datetime:
            eff_times.append(de.effective_datetime)
        for k, v in de.numeric_fields.items():
            numeric_values.setdefault(k, []).append((float(v), w))

    evt_type = Counter(evt_types).most_common(1)[0][0] if evt_types else "other"
    pol_w = {"positive": 0.0, "negative": 0.0, "neutral": 0.0}
    intensity = 0.0
    for pol, w in sentiments:
        pol_w[pol] += w
        if pol == "positive":
            intensity += w
        elif pol == "negative":
            intensity -= w
    total_w = sum(pol_w.values()) or 1.0
    if pol_w["positive"] >= max(pol_w["negative"], pol_w["neutral"]):
        pol = "positive"
    elif pol_w["negative"] >= pol_w["neutral"]:
        pol = "negative"
    else:
        pol = "neutral"
    intensity = max(-1.0, min(1.0, float(intensity / total_w)))

    severity = None
    if severities:
        severity = max(0.0, min(1.0, float(sum(severities) / (weight_sum or 1.0))))

    eff_canon = min(eff_times) if eff_times else None
    numeric_canon: Dict[str, float] = {}
    for k, rows in numeric_values.items():
        val = aggregate_numeric_field(rows)
        if val is not None:
            numeric_canon[k] = val

    disagree = 0.0
    for k, rows in numeric_values.items():
        vals = [v for v, _ in rows]
        if len(vals) >= 2:
            mu = sum(vals) / len(vals)
            var = sum((x - mu) ** 2 for x in vals) / (len(vals) - 1)
            disagree += min(1.0, var / (abs(mu) + 1e-6))
    disagree = max(0.0, min(1.0, float(disagree)))

    impact = float(1.0 - math.exp(-weight_sum))

    ev.event_type = evt_type
    ev.canonical_effective_datetime = eff_canon
    ev.canonical_payload = {
        "event_type": evt_type,
        "canonical_effective_datetime": eff_canon.isoformat()
        if eff_canon
        else None,
        "sentiment_polarity": pol,
        "sentiment_intensity": round(intensity, 4),
        "severity": severity,
        "numeric_fields": numeric_canon,
        "support_doc_count": len(docs),
        "support_weight_sum": round(weight_sum, 4),
        "disagreement_score": round(disagree, 4),
        "impact_weight": round(impact, 4),
    }

    session.merge(
        EventFeatures(
            event_id=ev.id,
            features={
                "event_type": evt_type,
                "impact_weight": impact,
                "severity": severity or 0.0,
                "sentiment_intensity": intensity,
                "disagreement": disagree,
                "support_doc_count": len(docs),
                "support_weight_sum": weight_sum,
                "primary_ticker": ev.primary_ticker or "",
                "target_count": len(
                    session.execute(
                        select(EventTarget).where(
                            EventTarget.event_id == ev.id
                        )
                    ).scalars().all()
                ),
            },
        )
    )
    session.flush()
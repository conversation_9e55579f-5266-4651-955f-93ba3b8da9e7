from __future__ import annotations

import json
import os
from dataclasses import dataclass
from typing import Any, Dict, Optional

from jsonschema import Draft202012Validator

from .schema import EXTRACTION_JSON_SCHEMA


JsonDict = Dict[str, Any]


def _validator() -> Draft202012Validator:
    return Draft202012Validator(EXTRACTION_JSON_SCHEMA)


@dataclass
class LLMResult:
    raw_text: str
    parsed_json: Optional[JsonDict]
    errors: Optional[str]


class LLMClientBase:
    def __init__(self) -> None:
        self.validator = _validator()

    def _validate(self, obj: JsonDict) -> Optional[str]:
        """Validate JSON object against schema and return error messages if any."""
        errors = []
        for error in self.validator.iter_errors(obj):
            path = "$" + "".join(
                f"[{repr(part)}]" if isinstance(part, int) else f".{part}"
                for part in error.path
            )
            errors.append(f"{path}: {error.message}")
        return "\n".join(errors) if errors else None

    def call(self, article: str, schema: JsonDict) -> LLMResult:
        """Extract structured data from article text using LLM."""
        raise NotImplementedError("Subclasses must implement call method")


class OpenAIClient(LLMClientBase):
    """
    Uses OpenAI Responses API with JSON Schema response_format if available.
    Falls back to Chat Completions with a strict prompt if needed.
    """

    def __init__(self, model: Optional[str] = None) -> None:
        super().__init__()
        from openai import OpenAI  # type: ignore

        self.client = OpenAI()
        self.model = model or os.environ.get("OPENAI_MODEL", "gpt-5-reasoning")

    def call(self, article: str, schema: JsonDict) -> LLMResult:
        schema_name = "NewsExtraction"
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": schema_name,
                "schema": schema,
                "strict": True,
            },
        }

        try:
            resp = self.client.responses.create(
                model=self.model,
                input=(
                    "You are a precise extraction engine for financial news.\n"
                    "Output only a JSON object conforming to the schema.\n"
                    "Use only facts in the article and include span citations.\n"
                    "If unknown, omit the field.\n"
                    f"Article:\n{article}"
                ),
                response_format=response_format,
            )
            txt = resp.output_text  # type: ignore[attr-defined]
            obj = json.loads(txt)
        except Exception:
            # Fallback to Chat Completions strict prompt
            chat = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "Return ONLY valid JSON.\n"
                            "It must conform to the JSON Schema.\n"
                            "No extra text."
                        ),
                    },
                    {
                        "role": "user",
                        "content": (
                            f"JSON Schema:\n{json.dumps(schema)}\n\n"
                            f"Article:\n{article}"
                        ),
                    },
                ],
                temperature=0.0,
            )
            txt = chat.choices[0].message.content or "{}"  # type: ignore
            obj = json.loads(txt)

        err = self._validate(obj)
        return LLMResult(raw_text=txt, parsed_json=None if err else obj, errors=err)


class AnthropicClient(LLMClientBase):
    """
    Uses Anthropic Messages with JSON tool (if available). Falls back to
    text prompting if needed.
    """

    def __init__(self, model: Optional[str] = None) -> None:
        from anthropic import Anthropic  # type: ignore

        super().__init__()
        self.client = Anthropic()
        self.model = model or os.environ.get("ANTHROPIC_MODEL", "claude-3-5-sonnet")

    def call(self, article: str, schema: JsonDict) -> LLMResult:
        prompt = (
            "You are a precise extraction engine for financial news.\n"
            "Return ONLY a JSON object that conforms to the JSON Schema.\n"
            "No commentary. Use span-level citations for every claim.\n"
            "If a field is unknown, omit it.\n"
            f"JSON Schema:\n{json.dumps(schema)}\n\n"
            f"Article:\n{article}"
        )
        try:
            msg = self.client.messages.create(
                model=self.model,
                max_tokens=2048,
                temperature=0,
                messages=[{"role": "user", "content": prompt}],
            )
            # Extract text content
            pieces = [c.text for c in msg.content if hasattr(c, "text")]  # type: ignore
            txt = "\n".join(pieces) if pieces else "{}"
            obj = json.loads(txt)
        except Exception as e:
            return LLMResult(raw_text=str(e), parsed_json=None, errors=str(e))

        err = self._validate(obj)
        return LLMResult(raw_text=txt, parsed_json=None if err else obj, errors=err)
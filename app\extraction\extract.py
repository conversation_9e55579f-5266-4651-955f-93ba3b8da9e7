from __future__ import annotations

import json
import re
from typing import Any, Dict, Optional, Tuple



from .llm_client import AnthropicClient, LLMClientBase, LLMResult, OpenAIClient
from .schema import EXTRACTION_JSON_SCHEMA, Extraction


def strip_code_fences(s: str) -> str:
    return re.sub(r"^```(?:json|JSON)?\s*|\s*```$", "", s.strip())


def extract_json_substring(s: str) -> Optional[str]:
    start = s.find("{")
    end = s.rfind("}")
    if start == -1 or end == -1 or end <= start:
        return None
    return s[start : end + 1]


def try_parse_json(s: str) -> <PERSON>ple[Optional[Dict[str, Any]], Optional[str]]:
    """Attempt to parse JSON from string, trying multiple cleanup strategies."""
    text = strip_code_fences(s)
    snippet = extract_json_substring(text) or text
    last_error = ""

    for candidate in [
        snippet,
        re.sub(r",\s*([}\]])", r"\1", snippet),  # Remove trailing commas
    ]:
        try:
            return json.loads(candidate), None
        except Exception as e:
            last_error = str(e)
    return None, last_error


def get_client(provider: str) -> LLMClientBase:
    if provider.lower() == "openai":
        return OpenAIClient()
    if provider.lower() == "anthropic":
        return AnthropicClient()
    raise ValueError(f"Unknown LLM provider: {provider}")


def extract_with_retries(
    article_text: str,
    provider: str = "openai",
    max_tries: int = 3,
) -> Extraction:
    client = get_client(provider)
    schema = EXTRACTION_JSON_SCHEMA
    last_err = ""
    last_raw = ""

    for _ in range(max_tries):
        result: LLMResult = client.call(article=article_text, schema=schema)
        last_raw = result.raw_text

        if result.errors:
            # Send validator feedback back in the prompt by wrapping the
            # schema in the provider call; for OpenAI Responses, we already
            # use strict JSON schema, so we simply retry.
            last_err = result.errors
        else:
            # Parsed JSON already schema-valid
            return Extraction.model_validate(result.parsed_json)

        # Try to salvage parseable JSON from the raw text as a last resort
        obj, parse_err = try_parse_json(last_raw)
        if obj:
            # Validate again
            err = client._validate(obj)  # noqa: SLF001 (intentional)
            if not err:
                return Extraction.model_validate(obj)
            last_err = err
        else:
            if parse_err:
                last_err = parse_err

    raise ValueError(
        "Extraction failed after retries.\n"
        f"Last error: {last_err}\nLast raw: {last_raw[:500]}"
    )
version: "3.8"

services:
  redpanda:
    image: docker.redpanda.com/redpandadata/redpanda:latest
    container_name: redpanda
    command:
      - redpanda
      - start
      - --smp=1
      - --overprovisioned
      - --reserve-memory=0M
      - --node-id=0
      - --kafka-addr=PLAINTEXT://0.0.0.0:9092
      - --advertise-kafka-addr=PLAINTEXT://redpanda:9092
      - --enable-schema-registry
    ports:
      - "9092:9092"
      - "9644:9644"
      - "8081:8081" # schema registry
    volumes:
      - redpanda-data:/var/lib/redpanda/data

  redpanda-console:
    image: docker.redpanda.com/redpandadata/console:latest
    container_name: redpanda-console
    environment:
      KAFKA_BROKERS: redpanda:9092
      SCHEMAREGISTRY_URL: http://redpanda:8081
    ports:
      - "8080:8080"

  postgres:
    image: postgres:16
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
      POSTGRES_DB: market_news
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  redis:
    image: redis:7
    ports:
      - "6379:6379"

volumes:
  redpanda-data: {}
  pgdata: {}
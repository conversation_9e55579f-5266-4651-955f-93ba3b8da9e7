from __future__ import annotations

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class Mention(BaseModel):
    ticker: str
    figi: Optional[str] = None
    confidence: float = Field(ge=0.0, le=1.0)


class Citation(BaseModel):
    field: str
    quote: str
    start_char: int
    end_char: int


class Extraction(BaseModel):
    source_url: str
    source_type: str  # "edgar", "business_wire", "prnewswire", "newswire", "web"
    vendor_id: Optional[str] = None
    published_at: str  # ISO-8601 with timezone
    effective_datetime: Optional[str] = None
    event_type: str  # "earnings", "guidance_up", "mna", ...
    eight_k_items: List[str] = []
    mentions: List[Mention]
    sentiment_polarity: str  # "positive" | "negative" | "neutral"
    sentiment_intensity: float = Field(ge=-1.0, le=1.0)
    severity: Optional[float] = Field(None, ge=0.0, le=1.0)
    numeric_fields: Dict[str, float] = {}
    uncertainty_flags: List[str] = []
    novelty_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    credibility_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    citations: List[Citation]


ALLOWED_SOURCE_TYPES = [
    "edgar",
    "business_wire",
    "prnewswire",
    "newswire",
    "web",
]

ALLOWED_EVENT_TYPES = [
    "earnings",
    "guidance_up",
    "guidance_down",
    "mna",
    "litigation",
    "regulatory",
    "product_recall",
    "security_breach",
    "leadership_change",
    "financing",
    "dividend",
    "buyback",
    "rating_change",
    "customer_win",
    "customer_loss",
    "bankruptcy",
    "trading_halt",
    "other",
]

ALLOWED_POLARITY = ["positive", "negative", "neutral"]

ALLOWED_UNCERTAINTY_FLAGS = [
    "may",
    "expects",
    "preliminary",
    "unaudited",
    "subject_to_approval",
    "nonbinding",
    "rumor",
]

EXTRACTION_JSON_SCHEMA: Dict[str, Any] = {
    "$schema": "https://json-schema.org/draft/2020-12/schema",
    "$id": "https://example.com/schemas/news_extraction.schema.json",
    "title": "NewsExtraction",
    "type": "object",
    "additionalProperties": False,
    "required": [
        "source_url",
        "source_type",
        "published_at",
        "event_type",
        "mentions",
        "sentiment_polarity",
        "sentiment_intensity",
        "citations",
    ],
    "properties": {
        "source_url": {"type": "string", "format": "uri", "maxLength": 2048},
        "source_type": {"type": "string", "enum": ALLOWED_SOURCE_TYPES},
        "vendor_id": {"type": "string", "maxLength": 128},
        "published_at": {"type": "string", "format": "date-time"},
        "effective_datetime": {"type": "string", "format": "date-time"},
        "event_type": {"type": "string", "enum": ALLOWED_EVENT_TYPES},
        "eight_k_items": {
            "type": "array",
            "items": {"type": "string", "pattern": r"^\d{1,2}\.\d{2}$"},
            "uniqueItems": True,
        },
        "mentions": {
            "type": "array",
            "minItems": 1,
            "items": {
                "type": "object",
                "additionalProperties": False,
                "required": ["ticker", "confidence"],
                "properties": {
                    "ticker": {
                        "type": "string",
                        "pattern": r"^[A-Z][A-Z0-9\.\-]{0,15}$",
                    },
                    "figi": {"type": "string"},
                    "confidence": {
                        "type": "number",
                        "minimum": 0.0,
                        "maximum": 1.0,
                    },
                },
            },
        },
        "sentiment_polarity": {"type": "string", "enum": ALLOWED_POLARITY},
        "sentiment_intensity": {
            "type": "number",
            "minimum": -1.0,
            "maximum": 1.0,
        },
        "severity": {"type": "number", "minimum": 0.0, "maximum": 1.0},
        "numeric_fields": {
            "type": "object",
            "additionalProperties": False,
            "patternProperties": {r"^[A-Za-z0-9_]+$": {"type": "number"}},
        },
        "uncertainty_flags": {
            "type": "array",
            "items": {"type": "string", "enum": ALLOWED_UNCERTAINTY_FLAGS},
            "uniqueItems": True,
        },
        "novelty_score": {"type": "number", "minimum": 0.0, "maximum": 1.0},
        "relevance_score": {"type": "number", "minimum": 0.0, "maximum": 1.0},
        "credibility_score": {"type": "number", "minimum": 0.0, "maximum": 1.0},
        "citations": {
            "type": "array",
            "minItems": 1,
            "items": {
                "type": "object",
                "additionalProperties": False,
                "required": ["field", "quote", "start_char", "end_char"],
                "properties": {
                    "field": {
                        "type": "string",
                        "pattern": (
                            r"^(numeric_fields\.[A-Za-z0-9_]+|"
                            r"event_type|effective_datetime|"
                            r"sentiment_polarity|sentiment_intensity|"
                            r"severity|eight_k_items|mentions)$"
                        ),
                    },
                    "quote": {"type": "string", "minLength": 1},
                    "start_char": {"type": "integer", "minimum": 0},
                    "end_char": {"type": "integer", "minimum": 0},
                },
            },
        },
    },
}
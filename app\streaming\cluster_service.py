from __future__ import annotations

import asyncio
import os

import uj<PERSON>
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from sqlalchemy import create_engine
from sqlalchemy.orm import Session
from sqlalchemy.pool import NullPool

from app.db.models import create_all
from app.extraction.schema import Extraction
from app.pipeline.cluster import (
    attach_or_create_cluster,
    upsert_document_with_extraction,
)
from app.streaming.kafka_config import Topics, brokers, group


SERVICE = "clusterer"


def db_url() -> str:
    return os.environ.get(
        "POSTGRES_DSN",
        "postgresql+psycopg://postgres:postgres@localhost:5432/market_news",
    )


async def run() -> None:
    engine = create_engine(db_url(), poolclass=NullPool, future=True)
    create_all(db_url())

    consumer = AIOKafkaConsumer(
        Topics.EXTRACTED_VALID,
        bootstrap_servers=brokers(),
        group_id=group(SERVICE),
        enable_auto_commit=False,
        value_deserializer=lambda v: ujson.loads(v) if v else None,
        key_deserializer=lambda v: v.decode() if v else None,
        auto_offset_reset="earliest",
    )
    producer = AIOKafkaProducer(
        bootstrap_servers=brokers(),
        linger_ms=5,
        acks="all",
        enable_idempotence=True,
        value_serializer=lambda v: ujson.dumps(v).encode(),
        key_serializer=lambda v: v.encode(),
    )

    await consumer.start()
    await producer.start()
    try:
        async for msg in consumer:
            data = msg.value or {}
            extraction = Extraction.model_validate(data)
            with engine.begin() as conn:
                session = Session(conn)
                doc, de = upsert_document_with_extraction(
                    session=session,
                    extraction=extraction,
                    title=data.get("title"),
                    raw_text=data.get("raw_text", ""),
                    language=data.get("language"),
                )
                cluster = attach_or_create_cluster(
                    session=session,
                    doc=doc,
                    de=de,
                    extraction=extraction,
                )
                await producer.send_and_wait(
                    Topics.CLUSTERS_UPDATED,
                    key=cluster.id,
                    value={
                        "event_id": cluster.id,
                        "event_type": cluster.event_type,
                        "primary_ticker": cluster.primary_ticker,
                        "canonical_payload": cluster.canonical_payload,
                    },
                )
                await consumer.commit()
    finally:
        await consumer.stop()
        await producer.stop()


if __name__ == "__main__":
    asyncio.run(run())
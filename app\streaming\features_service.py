from __future__ import annotations

import asyncio
import os

import uj<PERSON>
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from sqlalchemy import create_engine
from sqlalchemy.orm import Session
from sqlalchemy.pool import NullPool

from app.db.models import EventFeatures
from app.streaming.kafka_config import Topics, brokers, group


SERVICE = "features"


def db_url() -> str:
    return os.environ.get(
        "POSTGRES_DSN",
        "postgresql+psycopg://postgres:postgres@localhost:5432/market_news",
    )


async def run() -> None:
    engine = create_engine(db_url(), poolclass=NullPool, future=True)

    consumer = AIOKafkaConsumer(
        Topics.CLUSTERS_UPDATED,
        bootstrap_servers=brokers(),
        group_id=group(SERVICE),
        enable_auto_commit=False,
        value_deserializer=lambda v: ujson.loads(v) if v else None,
        key_deserializer=lambda v: v.decode() if v else None,
        auto_offset_reset="earliest",
    )
    producer = AIOKafkaProducer(
        bootstrap_servers=brokers(),
        linger_ms=5,
        acks="all",
        enable_idempotence=True,
        value_serializer=lambda v: ujson.dumps(v).encode(),
        key_serializer=lambda v: v.encode(),
    )

    await consumer.start()
    await producer.start()
    try:
        async for msg in consumer:
            event_id = msg.key or ""
            with engine.begin() as conn:
                session = Session(conn)
                ef = session.get(EventFeatures, event_id)
                feats = ef.features if ef else {}
            await producer.send_and_wait(
                Topics.FEATURES_READY,
                key=event_id,
                value={"event_id": event_id, "features": feats},
            )
            await consumer.commit()
    finally:
        await consumer.stop()
        await producer.stop()


if __name__ == "__main__":
    asyncio.run(run())